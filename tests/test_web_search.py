#!/usr/bin/env python3
"""
Web Search 测试脚本
用于测试 BigModel API 网络搜索功能
"""

import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.web_search import web_search, web_search_advanced

def test_basic_search():
    """测试基本搜索功能"""
    print("=== 测试基本搜索 ===")
    query = "人工智能最新发展"
    result = web_search(query, count=3)
    
    print(f"查询: {query}")
    print(f"成功: {result.get('success')}")
    
    if result.get('success'):
        print(f"结果数量: {result.get('total_results')}")
        for i, item in enumerate(result.get('results', []), 1):
            print(f"\n结果 {i}:")
            print(f"  标题: {item.get('title')}")
            print(f"  链接: {item.get('url')}")
            print(f"  摘要: {item.get('snippet')[:100]}...")
    else:
        print(f"错误: {result.get('error')}")
    
    print("\n" + "="*50 + "\n")

def test_advanced_search():
    """测试高级搜索功能"""
    print("=== 测试高级搜索 ===")
    query = "人工智能"
    result = web_search_advanced(
        query=query, 
        search_intent=True, 
        count=3, 
        content_size="high"
    )
    
    print(f"查询: {query}")
    print(f"搜索意图: True")
    print(f"内容大小: high")
    print(f"成功: {result.get('success')}")
    
    if result.get('success'):
        print(f"结果数量: {result.get('total_results')}")
        print(f"请求ID: {result.get('request_id')}")
        
        # 显示搜索意图信息
        search_intent = result.get('search_intent', [])
        if search_intent:
            print(f"搜索意图: {search_intent}")
        
        for i, item in enumerate(result.get('results', []), 1):
            print(f"\n结果 {i}:")
            print(f"  标题: {item.get('title')}")
            print(f"  链接: {item.get('url')}")
            print(f"  发布日期: {item.get('publish_date')}")
            print(f"  媒体: {item.get('media')}")
            print(f"  摘要: {item.get('snippet')[:100]}...")
    else:
        print(f"错误: {result.get('error')}")
    
    print("\n" + "="*50 + "\n")


def test_error_handling():
    """测试错误处理"""
    print("=== 测试错误处理 ===")
    
    # 测试空查询
    result = web_search("")
    print(f"空查询结果: {result}")
    
    # 测试无效内容大小
    result = web_search_advanced("test", content_size="invalid")
    print(f"无效内容大小结果: {result}")
    
    print("\n" + "="*50 + "\n")

def main():
    """主测试函数"""
    print("Web Search 功能测试")
    print("="*50)
    
    # 检查配置
    try:
        from src.core.config_manager import config_manager
        web_search_config = config_manager.get_module_config('web_search')
        api_key = web_search_config.get('api_key', '')
        
        if not api_key:
            print("警告: BigModel API key 未配置，BigModel 搜索可能失败")
            print("请在 resources/config.json 中配置 web_search.api_key")
        else:
            print(f"API Key 已配置: {api_key[:10]}...")
        
        print(f"Base URL: {web_search_config.get('base_url')}")
        print()
        
    except Exception as e:
        print(f"配置检查失败: {e}")
        return
    
    # 运行测试
    try:
        test_basic_search()
        test_advanced_search()
        test_error_handling()
        
        print("所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
