"""
配置管理模块
负责加载和管理项目配置
"""

import json
import logging
import os
from typing import Dict, Any


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "resources/config.json"):
        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.config_file = os.path.join(project_root, config_file)
        self._config = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logging.info(f"配置文件 {self.config_file} 加载成功")
            else:
                logging.warning(f"配置文件 {self.config_file} 不存在，使用默认配置")
                self._config = {}
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            self._config = {}


    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            logging.info(f"配置已保存到 {self.config_file}")
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取全局配置项"""
        return self._config.get(key, default)
    
    def get_module_config(self, module_name: str) -> Dict[str, Any]:
        """获取指定模块的配置"""
        return self._config.get(module_name, {})

    def set_module_config(self, module_name: str, config: Dict[str, Any]) -> None:
        """设置指定模块的配置"""
        self._config[module_name] = config

    def set_key_config(self, key: str,key_key:str, value: Any) -> None:
        """设置指定key的配置"""
        if key not in self._config:
            self._config[key] = {}
        self._config[key][key_key] = value

    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config.copy()

# 全局配置管理器实例
config_manager = ConfigManager()
