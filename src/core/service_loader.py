"""
服务加载器
负责加载和初始化所有MCP服务
"""

import importlib
import logging
import os
import sys
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.mcp_base import service_registry

logger = logging.getLogger('ServiceLoader')


class ServiceLoader:
    """服务加载器"""
    
    def __init__(self):
        self._loaded_services = set()
    
    def load_all_services(self) -> None:
        """加载所有MCP服务"""
        logger.info("开始加载MCP服务...")
        
        # 获取services目录
        services_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'services')
        
        if not os.path.exists(services_dir):
            logger.warning(f"服务目录不存在: {services_dir}")
            return
        
        # 扫描并导入所有服务模块
        service_files = []
        for file in os.listdir(services_dir):
            if file.endswith('.py') and file != '__init__.py':
                service_files.append(file[:-3])  # 移除.py扩展名
        
        logger.info(f"发现服务文件: {service_files}")
        
        # 导入服务模块（这会触发服务的自动注册）
        for service_name in service_files:
            try:
                self._load_service(service_name)
            except Exception as e:
                logger.error(f"加载服务 {service_name} 失败: {e}")
        
        # 显示加载结果
        ServiceLoader._show_loaded_services()
    
    def _load_service(self, service_name: str) -> None:
        """加载单个服务"""
        if service_name in self._loaded_services:
            logger.debug(f"服务 {service_name} 已加载，跳过")
            return
        
        try:
            module_name = f'src.services.{service_name}'
            importlib.import_module(module_name)
            self._loaded_services.add(service_name)
            logger.info(f"成功加载服务: {service_name}")
        except ImportError as e:
            logger.error(f"导入服务模块 {service_name} 失败: {e}")
            raise
        except Exception as e:
            logger.error(f"初始化服务 {service_name} 失败: {e}")
            raise
    @staticmethod
    def _show_loaded_services() -> None:
        """显示已加载的服务信息"""
        all_services = service_registry.get_all_services()
        registered_services = service_registry.get_all_registered_services()
        
        logger.info(f"服务加载完成，共发现 {len(all_services)} 个服务，其中 {len(registered_services)} 个已注册:")
        for service_name, service in all_services.items():
            if service_name in registered_services:
                status = "已注册（启用且配置有效）"
            elif service.is_enabled():
                is_valid, error_msg = service.validate_config()
                if is_valid:
                    status = "未注册（配置有效但未启用）"
                else:
                    status = f"未注册（配置无效: {error_msg}）"
            else:
                status = "未注册（未启用）"
            logger.info(f"  - {service_name}: {status} ({service.get_service_path()})")
    
    def reload_service(self, service_name: str) -> None:
        """重新加载指定服务"""
        logger.info(f"重新加载服务: {service_name}")
        
        # 从已加载列表中移除
        self._loaded_services.discard(service_name)
        
        # 重新加载
        self._load_service(service_name)
    
    def get_loaded_services(self) -> List[str]:
        """获取已加载的服务列表"""
        return list(self._loaded_services)


# 全局服务加载器实例
service_loader = ServiceLoader()
