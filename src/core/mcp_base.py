"""
MCP基础单例类
提供MCP服务的基础功能，包括配置管理、启用状态判断和注册功能
"""

import logging
import os
import sys
from abc import ABC, abstractmethod
from threading import Lock
from typing import Dict, Any, Optional

from src.constants import mcp_server_constants

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.config_manager import config_manager

logger = logging.getLogger('MCP_Base')


class MCPServiceMeta(type(ABC)):
    """MCP服务元类，实现单例模式，继承自ABC的元类以解决冲突"""
    _instances = {}
    _lock = Lock()

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    cls._instances[cls] = super(MCPServiceMeta, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class MCPServiceRegistry:
    """MCP服务注册表，管理所有MCP服务实例"""
    
    def __init__(self):
        self._registered_services: Dict[str, 'BaseMCPService'] = {}  # 已注册的服务（启用且配置有效）
        self._all_services: Dict[str, 'BaseMCPService'] = {}  # 所有服务实例
        self._lock = Lock()
    
    def register_service(self, service: 'BaseMCPService') -> None:
        """注册MCP服务 - 只有启用且配置有效的服务才会被注册"""
        with self._lock:
            service_name = service.get_service_name()
            
            # 将服务添加到所有服务列表中
            self._all_services[service_name] = service
            
            # 确保配置文件中有该服务的配置项
            MCPServiceRegistry._ensure_service_config(service)
            
            # 检查服务是否启用
            if not service.is_enabled():
                logger.info(f"MCP服务 {service_name} 未启用，跳过注册")
                return
            
            # 验证配置有效性
            is_valid, error_msg = service.validate_config()
            if not is_valid:
                logger.error(f"MCP服务 {service_name} 配置无效，跳过注册: {error_msg}")
                return
            
            # 注册服务
            if service_name in self._registered_services:
                logger.warning(f"MCP服务 {service_name} 已存在，将被覆盖")
            
            self._registered_services[service_name] = service
            logger.info(f"注册MCP服务: {service_name}")
    @staticmethod
    def _ensure_service_config(service: 'BaseMCPService') -> None:
        """确保服务在配置文件中有相应的配置项"""
        service_name = service.get_service_name()
        
        # 确保mcp_used中有该服务的启用状态
        mcp_used = config_manager.get("mcp_used", {})
        if service_name not in mcp_used:
            config_manager.set_key_config('mcp_used',service_name,mcp_server_constants.DEFAULT_MCP_USED)  # 默认禁用新服务
            logger.info(f"为新服务 {service_name} 添加默认配置")
        
        # 确保有服务的配置节
        service_config = config_manager.get_module_config(service_name)
        if not service_config:
            config_manager.set_module_config(service_name, {})

        # 保存配置
        config_manager.save_config()
    
    def unregister_service(self, service_name: str) -> None:
        """取消注册MCP服务"""
        with self._lock:
            if service_name in self._registered_services:
                del self._registered_services[service_name]
                logger.info(f"取消注册MCP服务: {service_name}")
    
    def get_registered_service(self, service_name: str) -> Optional['BaseMCPService']:
        """获取指定的启用的MCP服务"""
        return self._registered_services.get(service_name)
    
    def get_all_registered_services(self) -> Dict[str, 'BaseMCPService']:
        """获取所有注册的MCP服务"""
        return self._registered_services.copy()

    def list_registered_service_names(self) -> list:
        """列出所有启用的服务名称（已注册的服务都是启用且配置有效的）"""
        return list(self._registered_services.keys())
    
    def get_all_services(self) -> Dict[str, 'BaseMCPService']:
        """获取所有服务（包括未启用或配置无效的服务）"""
        return self._all_services.copy()
    
    def list_all_service_names(self) -> list:
        """列出所有服务名称（包括未启用的）"""
        return list(self._all_services.keys())


# 全局服务注册表实例
service_registry = MCPServiceRegistry()


class BaseMCPService(ABC, metaclass=MCPServiceMeta):
    """MCP服务基础类"""
    
    def __init__(self, service_name: str):
        self._service_name = service_name
        self._logger = logging.getLogger(f'MCP_{service_name}')
        
        # 自动注册到服务注册表（注册逻辑会检查启用状态和配置有效性）
        service_registry.register_service(self)
    
    def get_service_name(self) -> str:
        """获取服务名称"""
        return self._service_name
    
    @abstractmethod
    def get_service_path(self) -> str:
        """获取服务文件路径"""
        pass

    def is_enabled(self) -> bool:
        """检查当前MCP服务是否启用"""
        return self._service_name in service_registry.list_registered_service_names()
    
    def get_config(self, key: str = None, default: Any = None) -> Any:
        """获取服务配置项
        
        Args:
            key: 配置项键名，如果为None则返回整个配置字典
            default: 默认值
        """
        module_config = config_manager.get_module_config(self._service_name)
        
        if key is None:
            return module_config
        
        return module_config.get(key, default)
    
    def set_config(self, key: str, value: Any) -> None:
        """设置服务配置项"""
        module_config = config_manager.get_module_config(self._service_name)
        module_config[key] = value
        config_manager.set_module_config(self._service_name, module_config)
        config_manager.save_config()
        self._logger.info(f"更新配置项 {key} = {value}")
    
    @abstractmethod
    def validate_config(self) -> tuple[bool, str]:
        """验证配置项有效性
        
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        pass
    
    def get_logger(self) -> logging.Logger:
        """获取服务专用的日志记录器"""
        return self._logger
    
    def __str__(self) -> str:
        return f"MCPService({self._service_name}, enabled={self.is_enabled()})"
    
    def __repr__(self) -> str:
        return self.__str__()
