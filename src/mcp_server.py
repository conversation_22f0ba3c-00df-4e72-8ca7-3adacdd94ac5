"""
MCP服务启动器
用于启动配置文件中启用的MCP服务，并通过WebSocket与客户端通信
Version: 0.3.0

Usage:

python src/mcp_server.py

启动配置文件中启用的MCP服务
"""

import asyncio
import logging
import os
import random
import signal
import subprocess
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import websockets

from src.core.config_manager import config_manager
from src.core.mcp_base import service_registry
from src.core.service_loader import service_loader
from src.constants import global_constants

# Configure logging from config
log_level = getattr(logging, config_manager.get('log_level', 'INFO').upper())
log_format = config_manager.get('log_format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')

logging.basicConfig(
    level=log_level,
    format=log_format
)
logger = logging.getLogger('MCP_SERVER')

# Reconnection settings from config
INITIAL_BACKOFF = config_manager.get('initial_backoff', 1)
MAX_BACKOFF = config_manager.get('max_backoff', 600)
reconnect_attempt = 0
backoff = INITIAL_BACKOFF

async def connect_with_retry(uri):
    """Connect to WebSocket server with retry mechanism"""
    global reconnect_attempt, backoff
    while reconnect_attempt <global_constants. MAX_WEBSOCKET_RETRY_ATTEMPTS:
        try:
            if reconnect_attempt > 0:
                wait_time = backoff * (1 + random.random() * 0.1)  # Add some random jitter
                logger.info(f"Waiting {wait_time:.2f} seconds before reconnection attempt {reconnect_attempt}...")
                await asyncio.sleep(wait_time)

            # Attempt to connect
            await connect_to_server(uri)

        except Exception as e:
            reconnect_attempt += 1
            logger.warning(f"Connection closed (attempt: {reconnect_attempt}): {e}")
            
            # 检查是否达到最大重试次数
            if reconnect_attempt >= global_constants.MAX_WEBSOCKET_RETRY_ATTEMPTS:
                logger.error(f"达到最大重试次数 {global_constants.MAX_WEBSOCKET_RETRY_ATTEMPTS}，停止重连")
                raise Exception(f"WebSocket连接失败，已重试 {global_constants.MAX_WEBSOCKET_RETRY_ATTEMPTS} 次")
            
            # Calculate wait time for next reconnection (exponential backoff)
            backoff = min(backoff * 2, MAX_BACKOFF)

async def connect_to_server(uri):
    """Connect to WebSocket server and establish bidirectional communication with all enabled MCP services"""
    global reconnect_attempt, backoff
    processes = {}
    try:
        logger.info(f"Connecting to WebSocket server...")
        async with websockets.connect(uri) as websocket:
            logger.info(f"Successfully connected to WebSocket server")

            # Reset reconnection counter if connection closes normally
            reconnect_attempt = 0
            backoff = INITIAL_BACKOFF

            # Get all enabled MCP services
            enabled_mcps = service_registry.get_all_registered_services()
            
            # Start all MCP service processes
            for mcp_name, mcp_services in enabled_mcps.items():
                mcp_script=mcp_services.get_service_path()
                process = subprocess.Popen(
                    ['python', mcp_script],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    encoding='utf-8',
                    text=True  # Use text mode
                )
                processes[mcp_name] = process
                logger.info(f"Started {mcp_name} process: {mcp_script}")

            # Create tasks for all processes
            # Add WebSocket to processes communication
            tasks = [pipe_websocket_to_processes(websocket, processes)]

            # Add processes to WebSocket communication
            for mcp_name, process in processes.items():
                tasks.append(pipe_process_to_websocket(process, websocket, mcp_name))
                tasks.append(pipe_process_stderr_to_terminal(process, mcp_name))

            # Wait for all tasks
            await asyncio.gather(*tasks)
            
    except websockets.exceptions.ConnectionClosed as e:
        logger.error(f"WebSocket connection closed: {e}")
        raise  # Re-throw exception to trigger reconnection
    except Exception as e:
        logger.error(f"Connection error: {e}")
        raise  # Re-throw exception
    finally:
        # Ensure all child processes are properly terminated
        for mcp_name, process in processes.items():
            logger.info(f"Terminating {mcp_name} process")
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            logger.info(f"{mcp_name} process terminated")

async def pipe_websocket_to_processes(websocket, processes):
    """Read data from WebSocket and write to all process stdin"""
    try:
        while True:
            # Read message from WebSocket
            message = await websocket.recv()
            logger.debug(f"<< {message[:120]}...")

            # Write to all process stdin (in text mode)
            if isinstance(message, bytes):
                message = message.decode('utf-8')
            
            # Send message to all active processes
            for mcp_name, process in processes.items():
                try:
                    if process.stdin and not process.stdin.closed:
                        process.stdin.write(message + '\n')
                        process.stdin.flush()
                except Exception as e:
                    logger.warning(f"Failed to write to {mcp_name} process: {e}")
                    
    except Exception as e:
        logger.error(f"Error in WebSocket to processes pipe: {e}")
        raise  # Re-throw exception to trigger reconnection
    finally:
        # Close all process stdin
        for mcp_name, process in processes.items():
            try:
                if process.stdin and not process.stdin.closed:
                    process.stdin.close()
            except Exception as e:
                logger.warning(f"Failed to close stdin for {mcp_name}: {e}")

async def pipe_process_to_websocket(process, websocket, mcp_name):
    """Read data from process stdout and send to WebSocket"""
    try:
        while True:
            # Read data from process stdout
            data = await asyncio.get_event_loop().run_in_executor(
                None, process.stdout.readline
            )

            if not data:  # If no data, the process may have ended
                logger.info(f"{mcp_name} process has ended output")
                break

            # Send data to WebSocket
            logger.debug(f">> [{mcp_name}] {data[:120]}...")
            # In text mode, data is already a string, no need to decode
            await websocket.send(data)
    except Exception as e:
        logger.error(f"Error in {mcp_name} process to WebSocket pipe: {e}")
        raise  # Re-throw exception to trigger reconnection

async def pipe_process_stderr_to_terminal(process, mcp_name):
    """Read data from process stderr and print to terminal"""
    try:
        while True:
            # Read data from process stderr
            data = await asyncio.get_event_loop().run_in_executor(
                None, process.stderr.readline
            )

            if not data:  # If no data, the process may have ended
                logger.info(f"{mcp_name} process has ended stderr output")
                break

            # Print stderr data to terminal with service name prefix
            sys.stderr.write(f"[{mcp_name}] {data}")
            sys.stderr.flush()
    except Exception as e:
        logger.error(f"Error in {mcp_name} process stderr pipe: {e}")
        raise  # Re-throw exception to trigger reconnection

def signal_handler(sig, frame):
    """Handle interrupt signals"""
    logger.info("Received interrupt signal, shutting down...")
    sys.exit(0)

def check_enabled_mcps():
    """至少启用一个MCP服务"""
    registered_services=service_registry.list_registered_service_names()
    if len(registered_services)<1:
        logger.error("没有启用的MCP服务，程序不启动")
        logger.info("可用的MCP服务:")
        for mcp_name in service_registry.list_all_service_names():
            # 由于没有一个是注册的,那么全部都是禁用的
            logger.info(f"  - {mcp_name}: 禁用")
        logger.info("请在 resources/config.json 中的 mcp_used 配置项中启用至少一个MCP服务")
        sys.exit(1)

    logger.info(f"启用的MCP服务: {registered_services}")

if __name__ == "__main__":
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    # 加载所有MCP服务
    logger.info("正在加载MCP服务...")
    service_loader.load_all_services()

    # 解析命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg in ['--help', '-h']:
            print("用法: python src/mcp_server.py")
            print("  启动配置文件中启用的MCP服务")
            print("\n可用的MCP服务:")
            for name in service_registry.list_all_service_names():
                status = "启用" if name in service_registry.list_registered_service_names() else "禁用"
                print(f"  - {name}: {status}")
            print("\n要启用/禁用MCP服务，请修改 resources/config.json 中的 mcp_used 配置项")
            sys.exit(0)
        else:
            logger.error("不支持的参数，使用 --help 查看帮助")
            sys.exit(1)
    
    # 验证启用的MCP服务
    check_enabled_mcps()

    # 从配置获取endpoint URL
    endpoint_url = config_manager.get('endpoint_url')
    if not endpoint_url:
        logger.error("请在 resources/config.json 中设置有效的 endpoint_url")
        sys.exit(1)

    # Start main loop
    try:
        asyncio.run(connect_with_retry(endpoint_url))
    except KeyboardInterrupt:
        logger.info("Program interrupted by user")
    except Exception as e:
        logger.error(f"Program execution error: {e}")
