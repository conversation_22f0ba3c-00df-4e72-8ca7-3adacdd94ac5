# calculator.py
import math
import os
import random
import sys

from mcp.server.fastmcp import FastMCP

# 添加父目录到路径以导入配置管理器
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.core.mcp_base import BaseMCPService

# 定义服务名称,该值必须和配置文件中的key和mcp_used的配置开关一致
server_name='calculator'

class CalculatorService(BaseMCPService):
    """计算器MCP服务"""

    def __init__(self):
        super().__init__(server_name)

    def get_service_path(self) -> str:
        return os.path.abspath(__file__)

    def validate_config(self) -> tuple[bool, str]:
        """验证配置项有效性"""
        return True, ""


# 创建服务实例（这会自动注册到服务注册表）
calculator_service = CalculatorService()

logger = calculator_service.get_logger()

# Fix UTF-8 encoding for Windows console
if sys.platform == 'win32':
    sys.stderr.reconfigure(encoding='utf-8')
    sys.stdout.reconfigure(encoding='utf-8')

# 检查服务是否启用
if not calculator_service.is_enabled():
    logger.warning("计算器服务未启用，请在配置文件中启用")
    # 仍然创建MCP实例，但会在日志中提示

# Create an MCP server FastMCP实例使用单独进程是为了隔离性,避免不同的MCP之间相互干扰
mcp = FastMCP(server_name)

# Add an addition tool
@mcp.tool()
def calculator(python_expression: str) -> dict:
    """Execute mathematical calculations and evaluate Python expressions safely.
    
    This tool provides a secure environment for performing mathematical computations
    by evaluating Python expressions. It supports basic arithmetic operations,
    advanced mathematical functions, and random number generation while maintaining
    security through restricted execution context.
    
    Args:
        python_expression (str): A valid Python mathematical expression to evaluate.
            The expression can include:
            - Basic arithmetic: +, -, *, /, //, %, **
            - Mathematical functions from the math module (e.g., math.sin, math.cos, math.sqrt)
            - Random functions from the random module (e.g., random.random, random.randint)
            - Parentheses for grouping operations
            - Numeric literals (integers, floats, scientific notation)
            
            Examples of valid expressions:
            - "2 + 3 * 4"
            - "math.sqrt(16) + math.pi"
            - "math.sin(math.pi / 2)"
            - "random.randint(1, 100)"
            - "(5 + 3) ** 2 / 4"
            - "math.factorial(5)"
            - "math.log(100, 10)"
    
    Returns:
        dict: A dictionary containing the calculation result with the following structure:
        {
            "success": bool,  # True if calculation was successful, False if error occurred
            "result": Any,    # The calculated result (when success is True)
            "error": str      # Error message describing what went wrong (when success is False)
        }
        
        The result can be of various types depending on the expression:
        - int: For integer calculations
        - float: For floating-point calculations  
        - bool: For boolean expressions
        - complex: For complex number operations
    
    Security Features:
        - Restricted execution environment that only allows mathematical operations
        - No access to file system, network, or other system resources
        - Limited to math and random modules only
        - No import statements allowed or needed
        - Prevents execution of potentially harmful code
    
    Error Handling:
        The tool gracefully handles various types of errors:
        - Syntax errors in the expression
        - Mathematical errors (e.g., division by zero, domain errors)
        - Undefined variables or functions
        - Type errors in operations
        
    Examples:
        >>> calculator("2 + 3")
        {"success": True, "result": 5}
        
        >>> calculator("math.sqrt(25)")
        {"success": True, "result": 5.0}
        
        >>> calculator("1 / 0")
        {"success": False, "error": "division by zero"}
        
        >>> calculator("math.sin(math.pi / 2)")
        {"success": True, "result": 1.0}
        
        >>> calculator("random.randint(1, 10)")
        {"success": True, "result": 7}  # (random result between 1-10)
    
    Note:
        - The math and random modules are pre-imported and available without explicit import
        - Complex expressions with multiple operations are supported
        - Results are logged for debugging and monitoring purposes
        - All calculations are performed in a sandboxed environment for security
    """
    try:
        # 安全的表达式求值，只允许数学运算
        allowed_names = {
            "math": math,
            "random": random,
            "__builtins__": {},
        }
        result = eval(python_expression, allowed_names)
        logger.info(f"Calculating formula: {python_expression}, result: {result}")
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error calculating formula: {python_expression}, error: {str(e)}")
        return {"success": False, "error": str(e)}

# Start the server
if __name__ == "__main__":
    mcp.run(transport="stdio")
