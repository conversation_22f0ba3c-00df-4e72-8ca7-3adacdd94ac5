# web_search.py
import os
import sys
from typing import Dict, Any

import requests
from mcp.server.fastmcp import FastMCP

# 添加父目录到路径以导入配置管理器
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.core.mcp_base import BaseMCPService
from src.constants import mcp_server_constants

# mcp名,必须和配置文件中对应key一致
server_name='web_search'
class WebSearchService(BaseMCPService):
    """网络搜索MCP服务"""

    def __init__(self):
        super().__init__(server_name)

    def get_service_path(self) -> str:
        return os.path.abspath(__file__)

    def validate_config(self) -> tuple[bool, str]:
        """验证配置项有效性"""
        api_key = self.get_config('api_key', '')
        base_url = self.get_config('base_url', '')
        search_engine = self.get_config('search_engine', '')
        if not api_key:
            return False, "API key未配置"
        
        if not base_url:
            return False, "Base URL未配置"
        if not search_engine:
            return False, "搜索引擎未配置"
        
        if not base_url.startswith(('http://', 'https://')):
            return False, "Base URL格式无效，必须以http://或https://开头"
        if search_engine not in mcp_server_constants.SEARCH_ENGINE_OPTIONS:
            return False, f"搜索引擎配置无效，必须为{mcp_server_constants.SEARCH_ENGINE_OPTIONS}"
        
        return True, ""


# 创建服务实例（这会自动注册到服务注册表）
web_search_service = WebSearchService()

logger = web_search_service.get_logger()
# Fix UTF-8 encoding for Windows console
if sys.platform == 'win32':
    sys.stderr.reconfigure(encoding='utf-8')
    sys.stdout.reconfigure(encoding='utf-8')

# 检查服务是否启用
if not web_search_service.is_enabled():
    logger.warning("网络搜索服务未启用，请在配置文件中启用")
    # 仍然创建MCP实例，但会在日志中提示

# 创建服务器实例
mcp = FastMCP(server_name)

@mcp.tool()
def web_search(query: str,
               search_intent: bool = False, count: int = 5, 
               content_size: str = "medium") -> Dict[str, Any]:
    """Perform web search using BigModel API to retrieve relevant information from the internet.
    
    This tool enables comprehensive web search capabilities by leveraging BigModel's search API.
    It can retrieve up-to-date information from various web sources and return structured results
    with titles, URLs, content snippets, and publication dates.
    
    Args:
        query (str): The search query string. This should be a clear and specific search term
            or question that describes what information you're looking for. Examples:
            - "latest Python 3.12 features"
            - "how to implement OAuth2 authentication"
            - "climate change effects 2024"
            
        search_intent (bool, optional): Whether to perform search intent recognition.
            When enabled, the API will analyze and understand the user's search intent
            to provide more relevant results. Defaults to False for faster response times.
            
        count (int, optional): Number of search results to return. Must be between 1 and 10.
            Defaults to 5. Higher values provide more comprehensive results but may increase
            response time and token usage.
            
        content_size (str, optional): Controls the length and detail of returned web content.
            Available options:
            - "medium" (default): Returns summary information suitable for basic reasoning
              and general Q&A tasks. Provides a good balance between detail and conciseness.
            - "high": Returns maximum context with detailed content. Information-rich but
              more verbose, suitable for scenarios requiring comprehensive details and
              in-depth analysis.
    
    Returns:
        Dict[str, Any]: A dictionary containing the search results with the following structure:
        {
            "success": bool,  # True if search was successful, False otherwise
            "results": List[Dict],  # List of search result objects (when successful)
            "error": str  # Error message (when success is False)
        }
        
        Each result object in the "results" list contains:
        {
            "title": str,  # The title of the web page
            "url": str,    # The URL of the web page
            "snippet": str,  # Content excerpt or summary from the page
            "publish_date": str  # Publication date (if available)
        }
    
    Raises:
        No exceptions are raised directly. All errors are captured and returned in the
        response dictionary with success=False and an error message.
    
    Example:
        >>> result = web_search("Python async programming tutorial", count=3, content_size="high")
        >>> if result["success"]:
        ...     for item in result["results"]:
        ...         print(f"title: {item['title']}")
        ...         print(f"URL: {item['url']}")
        ...         print(f"Snippet: {item['snippet']}")
        ...         print(f"Publish date: {item['publish_date']}")
    
    Note:
        - Requires valid API key and base URL configuration in the service settings
        - Search results are limited to a maximum of 10 items per request
        - Network timeout is set to 30 seconds for API requests
        - The tool automatically handles API errors and network issues gracefully
    """
    try:
        # 获取配置
        api_key = web_search_service.get_config('api_key')
        base_url = web_search_service.get_config('base_url')
        search_engine = web_search_service.get_config('search_engine')

        # 验证参数
        if content_size not in mcp_server_constants.CONTENT_SIZE_OPTIONS:
            return {
                "success": False,
                "error": f"Invalid content size: {content_size}"
            }

        headers = {
            "Authorization": api_key,
            "Content-Type": "application/json"
        }

        payload = {
            "search_query": query,
            "search_engine": search_engine,
            "search_intent": search_intent,
            "count": min(count, 10),  # 限制最大结果数量
            "content_size": content_size
        }
        
        response = requests.post(
            base_url,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            api_result = response.json()
            # 解析搜索结果
            search_results = api_result.get("search_result", [])
            results = []
            
            for item in search_results:
                results.append({
                    "title": item.get("title", ""),
                    "url": item.get("link", ""),
                    "snippet": item.get("content", ""),
                    "publish_date": item.get("publish_date", "")
                })
            
            logger.info(f"BigModel search for '{query}' returned {len(results)} results")
            
            return {
                "success": True,
                "results": results,
            }
        else:
            logger.error(f"API request failed with status {response.status_code}: {response.text}")
            return {
                "success": False,
                "error": f"API request failed: {response.status_code}",
            }
                
    except requests.exceptions.RequestException as e:
        error_msg = f"Request error: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg
        }
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg
        }


#========== 启动 ==========

if __name__ == "__main__":
    mcp.run(transport="stdio")
