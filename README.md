# 小智MCP项目

这是一个基于配置文件的MCP（Model Context Protocol）管理系统，支持动态注册和管理多个MCP服务。

## 项目结构

```
xiaozhi-mcp/
├── src/                 # 源代码目录
│   ├── __init__.py      # 包初始化文件
│   ├── mcp_server.py    # MCP服务启动器（主入口）
│   ├── core/            # 核心模块目录
│   │   ├── __init__.py  # 包初始化文件
│   │   ├── config_manager.py    # 配置管理器
│   │   └── mcp_registry.py      # MCP注册管理器
│   └── services/        # MCP服务目录
│       ├── __init__.py  # 包初始化文件
│       ├── calculator.py    # 计算器MCP服务
│       └── web_search.py    # 网络搜索MCP服务
├── resources/           # 资源目录
│   └── config.json      # 配置文件
└── requirements.txt     # 依赖包
```

## 配置文件说明

`resources/config.json` 包含以下配置项：

### 全局配置
- `initial_backoff`: 初始重连等待时间（秒）
- `max_backoff`: 最大重连等待时间（秒）
- `endpoint_url`: WebSocket端点URL
- `log_level`: 日志级别（DEBUG, INFO, WARNING, ERROR）
- `log_format`: 日志格式

### MCP启用控制
- `mcp_used`: 控制各个MCP服务的启用状态
  - `calculator`: 计算器服务启用状态
  - `web_search`: 网络搜索服务启用状态

### 模块配置
每个MCP服务都有对应的配置节，以文件名为一级key：
- `calculator`: 计算器服务配置
- `web_search`: 网络搜索服务配置

## 使用方法

### 1. 启动MCP服务

```bash
# 启动配置文件中启用的MCP服务
python src/mcp_server.py

# 查看帮助信息
python src/mcp_server.py --help
```

### 2. 管理MCP服务

通过修改 `resources/config.json` 文件中的 `mcp_used` 配置项来启用或禁用MCP服务：

```json
{
  "mcp_used": {
    "calculator": true,     // 启用计算器服务
    "web_search": false     // 禁用网络搜索服务
  }
}
```

## 新增MCP服务（新架构）

基于新的单例类架构，添加MCP服务更加简单和自动化：

### 1. 创建服务文件

在 `src/services/` 目录下创建新的Python文件，继承 `BaseMCPService`：

```python
# src/services/my_service.py
import os
import sys
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP

# 导入基础类
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.core.mcp_base import BaseMCPService

class MyService(BaseMCPService):
    """我的MCP服务"""

    def __init__(self):
        super().__init__('my_service')  # 服务名称

    def get_service_name(self) -> str:
        return 'my_service'

    def get_service_path(self) -> str:
        return os.path.abspath(__file__)

    def get_default_config(self) -> Dict[str, Any]:
        return {
            'server_name': 'MyService',
            'description': '我的MCP服务'
        }

# 创建服务实例（自动注册到服务注册表）
my_service = MyService()

# 获取配置和日志
server_name = my_service.get_config('server_name', 'MyService')
logger = my_service.get_logger()

# 创建MCP服务器
mcp = FastMCP(server_name)

@mcp.tool()
def my_tool(input_text: str) -> dict:
    """我的工具描述"""
    try:
        result = f"处理结果: {input_text}"
        logger.info(f"处理请求: {input_text}")
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"处理失败: {e}")
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    mcp.run(transport="stdio")
```

### 2. 自动配置

服务首次运行时会自动在 `resources/config.json` 中添加配置：

```json
{
  "mcp_used": {
    "my_service": false  // 默认禁用，需要手动启用
  },
  "my_service": {
    "server_name": "MyService",
    "description": "我的MCP服务"
  }
}
```

### 3. 启用服务

在配置文件中启用服务：

```json
{
  "mcp_used": {
    "my_service": true
  }
}
```

详细文档请参考：[新架构文档](docs/new_architecture.md)

## 特性

- ✅ 配置文件驱动的MCP管理
- ✅ **新架构**：基于单例类的服务注册机制
- ✅ **主动注册**：服务主动注册，不依赖目录扫描
- ✅ **自动配置**：新服务自动添加配置项
- ✅ 启用/禁用控制
- ✅ 配置文件管理
- ✅ 自动重连机制
- ✅ 模块化配置管理
- ✅ 易于扩展新服务
- ✅ **统一接口**：所有服务使用统一的配置和日志接口

## 可用服务

### 1. Calculator（计算器）
提供基本的数学计算功能。

### 2. Web Search（网络搜索）
提供网络搜索功能，支持：
- **BigModel API搜索**：使用智谱AI的网络搜索API（主要方案）

#### Web Search 配置
```json
{
  "web_search": {
    "server_name": "SearchServer",
    "api_key": "your_bigmodel_api_key_here",
    "base_url": "https://open.bigmodel.cn/api/paas/v4/tools"
  }
}
```

#### 可用工具
- `web_search(query, max_results)`: 基本网络搜索
- `web_search_with_intent(query, intent, max_results)`: 带意图的搜索

详细使用说明请参考 [Web Search 使用文档](docs/web_search_usage.md)

## 测试

### 运行Web Search测试
```bash
python tests/test_web_search.py
```

## 依赖

```
python-dotenv>=1.0.0
websockets>=11.0.3
mcp>=1.8.1
pydantic>=2.11.4
requests>=2.31.0
```

## 注意事项

1. 首次运行前请确保在 `resources/config.json` 中设置正确的 `endpoint_url`
2. 新增MCP服务时，默认为禁用状态，需要在配置文件中启用
3. 配置文件修改后会自动保存，无需重启服务
4. 日志级别可以通过配置文件动态调整
5. MCP服务的启用/禁用完全通过配置文件控制，不支持命令行参数指定
